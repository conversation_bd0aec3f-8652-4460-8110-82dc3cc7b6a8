"""
Dataset processing utilities for reasoning models with think tokens
"""

import json
from typing import List, Dict, Any, Optional
from pathlib import Path

from datasets import load_dataset

from ..utils.logging import get_logger
from ..utils.helpers import generate_filename, ensure_directory

logger = get_logger("dataset_processing.dataset_processor")


class DatasetProcessor:
    """Processor for Hugging Face datasets with think token formatting"""

    def __init__(self, output_dir: str = "./data", think_start: str = "<think>", think_end: str = "</think>"):
        self.output_dir = Path(output_dir)
        self.think_start = think_start
        self.think_end = think_end
        ensure_directory(self.output_dir)

    def load_and_filter_dataset(
        self,
        dataset_name: str,
        split: str = "train",
        subset: Optional[str] = None,
        n_samples: Optional[int] = None,
        text_column: str = "solution",
        sort_by_length: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Load dataset from Hugging Face and filter for shortest text samples

        Args:
            dataset_name: Name of the dataset (e.g., "GAIR/LIMO")
            split: Dataset split (e.g., "train", "validation", "test")
            subset: Dataset subset (if applicable)
            n_samples: Number of samples to select (shortest if sort_by_length=True)
            text_column: Column to use for length calculation
            sort_by_length: Whether to sort by text length

        Returns:
            List of filtered dataset samples
        """
        logger.info(f"Loading dataset: {dataset_name}, split: {split}, subset: {subset}")

        try:
            # Load the dataset
            if subset:
                dataset = load_dataset(dataset_name, subset, split=split)
            else:
                dataset = load_dataset(dataset_name, split=split)

            logger.info(f"Loaded {len(dataset)} samples from {dataset_name}")

            # Convert to pandas for easier manipulation
            df = dataset.to_pandas()

            # Calculate text lengths if sorting is requested
            if sort_by_length and text_column in df.columns:
                df[f"{text_column}_length"] = df[text_column].str.len()
                df = df.sort_values(f"{text_column}_length")
                logger.info(f"Sorted by {text_column} length")

            # Select n_samples if specified
            if n_samples and n_samples < len(df):
                df = df.head(n_samples)
                logger.info(f"Selected {n_samples} samples")

                if sort_by_length and text_column in df.columns:
                    logger.info(
                        f"Text length range: {df[f'{text_column}_length'].min()} - {df[f'{text_column}_length'].max()}"
                    )

            # Convert to list of dictionaries
            selected_data = df.to_dict("records")

            # Clean up temporary length column
            for item in selected_data:
                if f"{text_column}_length" in item:
                    del item[f"{text_column}_length"]

            return selected_data

        except Exception as e:
            logger.error(f"Error loading/filtering dataset {dataset_name}: {e}")
            raise

    def format_reasoning_response(
        self,
        solution: str,
        answer: str
    ) -> dict:
        """
        Format solution for reasoning training (think tokens handled by tokenizer)

        Args:
            solution: The step-by-step solution
            answer: The final answer

        Returns:
            Dictionary with reasoning and answer fields
        """
        return {
            "reasoning": solution.strip(),
            "answer": answer.strip()
        }

    def save_reasoning_data(
        self,
        data: List[Dict[str, Any]],
        dataset_name: str,
        split: str,
        subset: Optional[str] = None,
        n_samples: Optional[int] = None,
        question_column: str = "question",
        solution_column: str = "solution",
        answer_column: str = "answer"
    ) -> str:
        """
        Save dataset as JSONL in reasoning format with think tokens

        Args:
            data: Dataset samples
            dataset_name: Name of the dataset
            split: Dataset split
            subset: Dataset subset
            n_samples: Number of samples
            question_column: Column name for questions
            solution_column: Column name for solutions
            answer_column: Column name for answers

        Returns:
            Path to saved file
        """
        filename = generate_filename(
            dataset_name, split, subset, n_samples, "reasoning"
        )
        filepath = self.output_dir / filename

        logger.info(f"Saving reasoning data to {filepath}")

        with open(filepath, "w", encoding="utf-8") as f:
            for item in data:
                # Format reasoning response
                reasoning_data = self.format_reasoning_response(
                    item[solution_column],
                    item[answer_column]
                )

                # Format for training (think tokens handled by tokenizer/template)
                training_item = {
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a helpful assistant.",
                        },
                        {
                            "role": "user",
                            "content": item[question_column],
                        },
                        {
                            "role": "assistant",
                            "content": reasoning_data["answer"],
                            "reasoning": reasoning_data["reasoning"]
                        },
                    ]
                }
                f.write(json.dumps(training_item, ensure_ascii=False) + "\n")

        logger.info(f"Saved {len(data)} reasoning samples to {filepath}")
        return str(filepath)

    def validate_data_quality(
        self,
        data: List[Dict[str, Any]],
        question_column: str = "question",
        solution_column: str = "solution",
        answer_column: str = "answer",
        min_solution_length: int = 10,
        max_solution_length: int = 10000
    ) -> List[Dict[str, Any]]:
        """
        Validate and filter data based on quality criteria

        Args:
            data: Dataset samples
            question_column: Column name for questions
            solution_column: Column name for solutions
            answer_column: Column name for answers
            min_solution_length: Minimum solution length
            max_solution_length: Maximum solution length

        Returns:
            Filtered dataset samples
        """
        valid_data = []

        for item in data:
            # Check required fields exist
            if not all(col in item for col in [question_column, solution_column, answer_column]):
                logger.warning(f"Skipping item missing required columns")
                continue

            # Check solution length
            solution_len = len(item[solution_column])
            if solution_len < min_solution_length or solution_len > max_solution_length:
                logger.warning(f"Skipping item with solution length {solution_len}")
                continue

            # Check for empty fields
            if not all(str(item[col]).strip() for col in [question_column, solution_column, answer_column]):
                logger.warning(f"Skipping item with empty fields")
                continue

            valid_data.append(item)

        logger.info(f"Validated {len(valid_data)}/{len(data)} samples")
        return valid_data

    def load_jsonl_data(self, filepath: str) -> List[Dict[str, Any]]:
        """
        Load data from JSONL file

        Args:
            filepath: Path to JSONL file

        Returns:
            List of loaded samples
        """
        data = []
        with open(filepath, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line:
                    data.append(json.loads(line))

        logger.info(f"Loaded {len(data)} samples from {filepath}")
        return data

    def process_dataset_for_reasoning(
        self,
        dataset_name: str,
        split: str = "train",
        subset: Optional[str] = None,
        n_samples: Optional[int] = None,
        question_column: str = "question",
        solution_column: str = "solution",
        answer_column: str = "answer",
        validate_quality: bool = True
    ) -> str:
        """
        Complete pipeline to process a dataset for reasoning training

        Args:
            dataset_name: Name of the dataset (e.g., "GAIR/LIMO")
            split: Dataset split
            subset: Dataset subset
            n_samples: Number of samples to process
            question_column: Column name for questions
            solution_column: Column name for solutions
            answer_column: Column name for answers
            validate_quality: Whether to validate data quality

        Returns:
            Path to saved reasoning dataset file
        """
        logger.info(f"Processing {dataset_name} for reasoning training")

        # Load and filter dataset
        data = self.load_and_filter_dataset(
            dataset_name=dataset_name,
            split=split,
            subset=subset,
            n_samples=n_samples,
            text_column=solution_column
        )

        # Validate data quality if requested
        if validate_quality:
            data = self.validate_data_quality(
                data=data,
                question_column=question_column,
                solution_column=solution_column,
                answer_column=answer_column
            )

        # Save reasoning data with think tokens
        filepath = self.save_reasoning_data(
            data=data,
            dataset_name=dataset_name,
            split=split,
            subset=subset,
            n_samples=len(data),
            question_column=question_column,
            solution_column=solution_column,
            answer_column=answer_column
        )

        logger.info(f"Successfully processed {len(data)} samples for reasoning training")
        return filepath
