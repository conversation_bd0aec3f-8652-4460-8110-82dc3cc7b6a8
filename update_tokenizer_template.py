#!/usr/bin/env python3
"""
Update existing LoRA adapter with think-aware chat template.

This script updates the tokenizer in an existing LoRA adapter directory
to include the custom Jinja template that supports think tokens.
"""

import os
import sys
import argparse
from pathlib import Path

# Add src to path
sys.path.append('src')

from utils.model_utils import setup_tokenizer_with_think_tokens, save_tokenizer_with_think_template
from transformers import AutoTokenizer


def test_template(tokenizer, think_mode=True):
    """Test the think-aware template."""
    messages = [
        {"role": "user", "content": "What is 15 + 27?"}
    ]
    
    # Test with think_mode parameter
    result = tokenizer.apply_chat_template(
        messages, 
        add_generation_prompt=True, 
        tokenize=False,
        think_mode=think_mode
    )
    
    return result


def main():
    parser = argparse.ArgumentParser(description="Update LoRA adapter with think-aware chat template")
    parser.add_argument("lora_path", help="Path to LoRA adapter directory")
    parser.add_argument("--base_model", default="Qwen/Qwen2.5-7B-Instruct", 
                       help="Base model name for tokenizer reference")
    parser.add_argument("--test", action="store_true", 
                       help="Test the template after updating")
    
    args = parser.parse_args()
    
    lora_path = Path(args.lora_path)
    
    if not lora_path.exists():
        print(f"❌ LoRA path does not exist: {lora_path}")
        return 1
    
    print(f"🔄 Updating LoRA adapter: {lora_path}")
    print(f"📋 Base model: {args.base_model}")
    print()
    
    try:
        # Create tokenizer with think-aware template
        print("🔧 Creating tokenizer with think-aware template...")
        tokenizer = setup_tokenizer_with_think_tokens(args.base_model)
        
        # Check if think tokens exist
        think_start_id = tokenizer.convert_tokens_to_ids("<think>")
        think_end_id = tokenizer.convert_tokens_to_ids("</think>")
        
        if think_start_id == tokenizer.unk_token_id:
            print("⚠️ Think tokens not found in vocabulary - adding them...")
        else:
            print(f"✅ Think tokens found: <think>={think_start_id}, </think>={think_end_id}")
        
        # Save updated tokenizer to LoRA directory
        print(f"💾 Saving updated tokenizer to {lora_path}...")
        save_tokenizer_with_think_template(tokenizer, str(lora_path))
        
        print("✅ Successfully updated LoRA adapter with think-aware template!")
        
        # Test the template if requested
        if args.test:
            print("\n🧪 Testing think-aware template...")
            print("=" * 50)
            
            # Test think mode ON
            result_on = test_template(tokenizer, think_mode=True)
            print("🟢 Think Mode ON:")
            print(repr(result_on))
            print()
            print("Formatted:")
            print(result_on)
            print()
            
            # Test think mode OFF
            result_off = test_template(tokenizer, think_mode=False)
            print("🔴 Think Mode OFF:")
            print(repr(result_off))
            print()
            print("Formatted:")
            print(result_off)
            print()
            
            print("✅ Template test completed!")
        
        print("\n🎉 LoRA adapter is now ready for universal think token support!")
        print("📝 Any code using tokenizer.apply_chat_template() will now support:")
        print("   - think_mode=True  → Triggers thinking mode")
        print("   - think_mode=False → Skips thinking mode")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error updating LoRA adapter: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
