#!/usr/bin/env python3
"""
KULLM Pro Code Switching Module

This module provides code switching functionality using OpenAI's Batch API to convert
English text to Korean. It can process any Hugging Face dataset with flexible parameters
and generate both original and code-switched JSONL files.

Features:
- Process any Hugging Face dataset with flexible parameters
- Filter and select n shortest samples based on character count
- Use OpenAI Batch API for efficient code switching
- Generate descriptive JSONL filenames
- Output original and code-switched versions
- Python Fire CLI interface

Example usage:
    python src/code_switch.py --dataset_name="GAIR/LIMO" --split="train" --n_samples=300 --subset=None
    python src/code_switch.py --dataset_name="microsoft/orca-math-word-problems-200k" --split="train" --n_samples=100
"""

import asyncio
import json
import logging
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import fire
from dotenv import load_dotenv
import openai
from tqdm import tqdm

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.data_processing import (
    load_hf_dataset,
    filter_shortest_samples,
    save_jsonl,
    create_output_filename,
    validate_jsonl_format,
)

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CodeSwitchingPipeline:
    """
    Pipeline for code switching datasets from English to Korean using OpenAI Batch API.
    Specifically designed to translate only the 'solution' column while preserving
    'question' and 'answer' columns unchanged.
    """

    def __init__(
        self,
        output_dir: str = "./data",
        api_key: Optional[str] = None,
        system_prompt_path: str = "code_switch.txt",
        max_completion_tokens: int = 8000,
    ):
        """
        Initialize the code switching pipeline.

        Args:
            output_dir: Directory to save output files
            api_key: OpenAI API key (if not provided, will use OPENAI_API_KEY env var)
            system_prompt_path: Path to the system prompt file
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.max_completion_tokens = max_completion_tokens

        # Setup OpenAI client
        api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError(
                "OpenAI API key not found. Set OPENAI_API_KEY environment variable."
            )

        self.client = openai.OpenAI(api_key=api_key)

        # Load system prompt from file
        self.system_prompt = self.load_system_prompt(system_prompt_path)

    def load_system_prompt(self, system_prompt_path: str) -> str:
        """
        Load system prompt from file.

        Args:
            system_prompt_path: Path to the system prompt file

        Returns:
            System prompt content
        """
        try:
            with open(system_prompt_path, "r", encoding="utf-8") as f:
                prompt = f.read().strip()
            logger.info(f"Loaded system prompt from {system_prompt_path}")
            return prompt
        except FileNotFoundError:
            logger.error(f"System prompt file not found: {system_prompt_path}")
            # Fallback to default prompt
            return """You are a sophisticated linguistic agent that performs concept-based code-switching between English and Korean.
Transform the given English reasoning trace into a conceptually efficient, code-switched sentence that blends English and Korean.
Use Korean as the matrix language (SOV structure) and embed English concepts where they are more efficient.
Apply Korean particles to all nouns and use Korean verb conjugations.
Focus on conceptual efficiency rather than word-for-word translation."""

    def prepare_batch_requests(
        self, data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Prepare batch requests for OpenAI API to translate only the 'solution' column.

        Args:
            data: List of data samples with 'question', 'solution', and 'answer' columns

        Returns:
            List of batch request objects for solution translation
        """
        requests = []

        for i, sample in enumerate(data):
            # Only process the 'solution' column
            if (
                "solution" in sample
                and isinstance(sample["solution"], str)
                and sample["solution"].strip()
            ):
                solution_text = sample["solution"].strip()

                # Check input length and warn if very long
                if len(solution_text) > 10000:
                    logger.warning(
                        f"Sample {i} has very long solution ({len(solution_text)} chars), may cause processing issues"
                    )

                # Add explicit instruction with preservation requirements
                user_content = f"""CRITICAL: Transform the following English mathematical reasoning into Korean-English code-switched text with COMPLETE PRESERVATION:

1. PRESERVE EVERY SENTENCE (one-to-one correspondence)
2. PRESERVE ALL thinking markers (Okay, Hmm, Wait, Let me, etc.)
3. PRESERVE ALL mathematical formatting (**Final Answer**, \\boxed{{}}, etc.)
4. PRESERVE ALL calculation steps and verifications
5. NO SUMMARIZATION - transform every sentence

Text to transform:
'{solution_text}'"""

                request = {
                    "custom_id": f"sample_{i}_solution",
                    "method": "POST",
                    "url": "/v1/chat/completions",
                    "body": {
                        "model": "o4-mini-2025-04-16",
                        "messages": [
                            {
                                "role": "user",
                                "content": f"{self.system_prompt}\n\n{user_content}",
                            }
                        ],
                        "max_completion_tokens": self.max_completion_tokens,
                    },
                }
                requests.append(request)
            else:
                logger.warning(f"Sample {i} missing or empty 'solution' column")

        logger.info(f"Prepared {len(requests)} batch requests for solution translation")
        return requests

    def create_batch_file(self, requests: List[Dict[str, Any]]) -> str:
        """
        Create a batch file for OpenAI API.

        Args:
            requests: List of batch requests

        Returns:
            Path to the created batch file
        """
        batch_file_path = self.output_dir / "batch_requests.jsonl"

        with open(batch_file_path, "w", encoding="utf-8") as f:
            for request in requests:
                f.write(json.dumps(request) + "\n")

        logger.info(f"Created batch file: {batch_file_path}")
        return str(batch_file_path)

    async def submit_batch_job(self, batch_file_path: str) -> str:
        """
        Submit a batch job to OpenAI API.

        Args:
            batch_file_path: Path to the batch file

        Returns:
            Batch job ID
        """
        # Upload the batch file
        with open(batch_file_path, "rb") as f:
            batch_input_file = self.client.files.create(file=f, purpose="batch")

        # Create the batch job
        batch_job = self.client.batches.create(
            input_file_id=batch_input_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h",
            metadata={"description": "KULLM Pro code switching batch"},
        )

        logger.info(f"Submitted batch job: {batch_job.id}")
        return batch_job.id

    async def wait_for_batch_completion(
        self, batch_id: str, check_interval: int = 60
    ) -> Dict[str, Any]:
        """
        Wait for batch job completion.

        Args:
            batch_id: Batch job ID
            check_interval: Check interval in seconds

        Returns:
            Completed batch job information
        """
        logger.info(f"Waiting for batch {batch_id} to complete...")

        while True:
            batch_job = self.client.batches.retrieve(batch_id)

            if batch_job.status == "completed":
                logger.info(f"Batch {batch_id} completed successfully")
                return batch_job
            elif batch_job.status == "failed":
                raise Exception(f"Batch {batch_id} failed: {batch_job.errors}")
            elif batch_job.status == "cancelled":
                raise Exception(f"Batch {batch_id} was cancelled")

            logger.info(
                f"Batch status: {batch_job.status}. Checking again in {check_interval} seconds..."
            )
            await asyncio.sleep(check_interval)

    def download_batch_results(self, batch_job: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Download and parse batch results.

        Args:
            batch_job: Completed batch job information

        Returns:
            List of batch results
        """
        # Download the output file
        output_file_id = batch_job.output_file_id
        output_content = self.client.files.content(output_file_id)

        # Parse results
        results = []
        for line in output_content.text.split("\n"):
            if line.strip():
                results.append(json.loads(line))

        logger.info(f"Downloaded {len(results)} batch results")
        return results

    def apply_translations(
        self, original_data: List[Dict[str, Any]], batch_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Apply translations to original data, only modifying the 'solution' column.

        Args:
            original_data: Original dataset with 'question', 'solution', 'answer' columns
            batch_results: Batch translation results for solution column

        Returns:
            Code-switched dataset with translated solutions and unchanged questions/answers
        """
        # Create a mapping of custom_id to translation
        translations = {}
        failed_requests = []

        for result in batch_results:
            custom_id = result["custom_id"]

            if result.get("response") and result["response"].get("body"):
                if (
                    result["response"]["body"].get("choices")
                    and len(result["response"]["body"]["choices"]) > 0
                ):
                    content = result["response"]["body"]["choices"][0]["message"][
                        "content"
                    ]
                    if content and content.strip():
                        translations[custom_id] = content
                        logger.debug(f"Successfully processed {custom_id}")
                    else:
                        logger.warning(f"Empty content for {custom_id}")
                        failed_requests.append(custom_id)
                else:
                    logger.warning(f"No choices in response for {custom_id}")
                    failed_requests.append(custom_id)
            elif result.get("error"):
                logger.error(f"API error for {custom_id}: {result['error']}")
                failed_requests.append(custom_id)
            else:
                logger.warning(f"Invalid response structure for {custom_id}")
                failed_requests.append(custom_id)

        if failed_requests:
            logger.warning(
                f"Failed to process {len(failed_requests)} requests: {failed_requests}"
            )

        # Apply translations to create code-switched data
        code_switched_data = []
        for i, sample in enumerate(original_data):
            # Create new sample with same structure
            new_sample = {
                "question": sample.get("question", ""),
                "solution": sample.get("solution", ""),
                "answer": sample.get("answer", ""),
            }

            # Only replace the solution with translated version if available
            custom_id = f"sample_{i}_solution"
            if custom_id in translations:
                new_sample["solution"] = translations[custom_id]
                logger.debug(f"Applied translation for sample {i}")
            else:
                logger.warning(
                    f"No translation found for sample {i}, keeping original solution"
                )

            code_switched_data.append(new_sample)

        logger.info(
            f"Applied solution translations to {len(code_switched_data)} samples"
        )
        return code_switched_data

    async def process_dataset(
        self,
        dataset_name: str,
        split: str = "train",
        subset: Optional[str] = None,
        n_samples: Optional[int] = None,
    ) -> Tuple[str, str]:
        """
        Process a dataset for code switching, translating only the 'solution' column.

        Args:
            dataset_name: Name of the Hugging Face dataset
            split: Dataset split to process
            subset: Dataset subset/configuration
            n_samples: Number of samples to process (shortest by character count)

        Returns:
            Tuple of (original_file_path, code_switched_file_path)
        """
        logger.info(f"Processing dataset: {dataset_name}")
        logger.info(f"Split: {split}, Subset: {subset}, Samples: {n_samples}")
        logger.info("Code switching will only translate the 'solution' column")

        # Load dataset
        dataset = load_hf_dataset(dataset_name, split, subset)
        data = list(dataset)

        # Validate that data has required columns
        if data:
            sample = data[0]
            required_columns = ["question", "solution", "answer"]
            missing_columns = [col for col in required_columns if col not in sample]
            if missing_columns:
                logger.warning(f"Missing columns in dataset: {missing_columns}")
                logger.info(f"Available columns: {list(sample.keys())}")

        # Filter to shortest samples if n_samples specified
        if n_samples and n_samples < len(data):
            # Use solution column for length calculation
            data = filter_shortest_samples(data, n_samples, text_columns=["solution"])

        # Generate filenames
        base_filename = create_output_filename(dataset_name, split, subset, len(data))
        original_filename = f"original_{base_filename}"
        code_switched_filename = f"code_switched_{base_filename}"

        original_path = self.output_dir / original_filename
        code_switched_path = self.output_dir / code_switched_filename

        # Save original data
        save_jsonl(data, original_path)

        # Prepare and submit batch job for solution translation only
        requests = self.prepare_batch_requests(data)
        if not requests:
            logger.error("No valid solution content found for translation")
            raise ValueError("No valid solution content found for translation")

        batch_file_path = self.create_batch_file(requests)

        batch_id = await self.submit_batch_job(batch_file_path)
        batch_job = await self.wait_for_batch_completion(batch_id)

        # Download results and apply translations
        batch_results = self.download_batch_results(batch_job)
        code_switched_data = self.apply_translations(data, batch_results)

        # Save code-switched data
        save_jsonl(code_switched_data, code_switched_path)

        # Cleanup batch file
        os.remove(batch_file_path)

        logger.info(f"Code switching completed!")
        logger.info(f"Original file: {original_path}")
        logger.info(f"Code-switched file: {code_switched_path}")

        return str(original_path), str(code_switched_path)


class CodeSwitchCLI:
    """
    Command-line interface for code switching using Python Fire.
    """

    def __init__(self):
        """Initialize the CLI."""
        self.logger = logger

    def run(
        self,
        dataset_name: str,
        split: str = "train",
        subset: Optional[str] = None,
        n_samples: Optional[int] = None,
        output_dir: str = "./data",
        system_prompt_path: str = "code_switch.txt",
        max_completion_tokens: int = 8000,
    ):
        """
        Run concept-based code switching on a dataset, translating only the 'solution' column.

        Uses sophisticated linguistic theories to create Korean-English code-switched text
        that leverages the unique strengths of each language for maximum conceptual efficiency.

        Args:
            dataset_name: Name of the Hugging Face dataset (e.g., "GAIR/LIMO")
            split: Dataset split to process (default: "train")
            subset: Dataset subset/configuration (default: None)
            n_samples: Number of shortest samples to process (default: None for all)
            output_dir: Output directory for files (default: "./data")
            system_prompt_path: Path to system prompt file (default: "code_switch.txt")

        Example:
            python src/code_switch.py run --dataset_name="GAIR/LIMO" --split="train" --n_samples=300
        """
        # Create pipeline with custom system prompt path
        pipeline = CodeSwitchingPipeline(
            output_dir=output_dir,
            system_prompt_path=system_prompt_path,
            max_completion_tokens=max_completion_tokens,
        )

        # Run the processing
        try:
            original_file, code_switched_file = asyncio.run(
                pipeline.process_dataset(
                    dataset_name=dataset_name,
                    split=split,
                    subset=subset,
                    n_samples=n_samples,
                )
            )

            self.logger.info("Code switching completed successfully!")
            self.logger.info(f"Original file: {original_file}")
            self.logger.info(f"Code-switched file: {code_switched_file}")
            self.logger.info("Only the 'solution' column was translated to Korean")

            return {
                "original_file": original_file,
                "code_switched_file": code_switched_file,
                "status": "success",
                "translated_columns": ["solution"],
            }

        except Exception as e:
            self.logger.error(f"Code switching failed: {e}")
            raise


def main():
    """Main entry point for the CLI."""
    fire.Fire(CodeSwitchCLI)


if __name__ == "__main__":
    main()
