#!/usr/bin/env python3
"""
KULLM Pro Code Switching Module

This module provides code switching functionality using OpenAI's Batch API to convert
English text to Korean. It can process any Hugging Face dataset with flexible parameters
and generate both original and code-switched JSONL files.

Features:
- Process any Hugging Face dataset with flexible parameters
- Filter and select n shortest samples based on character count
- Use OpenAI Batch API for efficient code switching
- Generate descriptive JSONL filenames
- Output original and code-switched versions
- Python Fire CLI interface

Example usage:
    python src/code_switch.py --dataset_name="GAIR/LIMO" --split="train" --n_samples=300 --subset=None
    python src/code_switch.py --dataset_name="microsoft/orca-math-word-problems-200k" --split="train" --n_samples=100
"""

import asyncio
import json
import logging
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import fire
from dotenv import load_dotenv
import openai
from tqdm import tqdm

from utils.data_processing import (
    load_hf_dataset,
    filter_shortest_samples,
    save_jsonl,
    create_output_filename,
    validate_jsonl_format
)

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CodeSwitchingPipeline:
    """
    Pipeline for code switching datasets from English to Korean using OpenAI Batch API.
    """

    def __init__(self, output_dir: str = "./data", api_key: Optional[str] = None):
        """
        Initialize the code switching pipeline.

        Args:
            output_dir: Directory to save output files
            api_key: OpenAI API key (if not provided, will use OPENAI_API_KEY env var)
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Setup OpenAI client
        api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")

        self.client = openai.OpenAI(api_key=api_key)

        # Default system prompt for code switching
        self.system_prompt = """You are a helpful assistant that translates English text to Korean.
Translate the given English text to natural, fluent Korean while preserving the meaning and context.
For mathematical expressions, formulas, and code, keep them as they are.
Only translate the natural language portions."""

    def prepare_batch_requests(self, data: List[Dict[str, Any]],
                             text_columns: List[str]) -> List[Dict[str, Any]]:
        """
        Prepare batch requests for OpenAI API.

        Args:
            data: List of data samples
            text_columns: List of column names to translate

        Returns:
            List of batch request objects
        """
        requests = []

        for i, sample in enumerate(data):
            for col in text_columns:
                if col in sample and isinstance(sample[col], str):
                    request = {
                        "custom_id": f"sample_{i}_{col}",
                        "method": "POST",
                        "url": "/v1/chat/completions",
                        "body": {
                            "model": "gpt-4o-mini",
                            "messages": [
                                {"role": "system", "content": self.system_prompt},
                                {"role": "user", "content": sample[col]}
                            ],
                            "max_tokens": 4000,
                            "temperature": 0.1
                        }
                    }
                    requests.append(request)

        logger.info(f"Prepared {len(requests)} batch requests")
        return requests

    def create_batch_file(self, requests: List[Dict[str, Any]]) -> str:
        """
        Create a batch file for OpenAI API.

        Args:
            requests: List of batch requests

        Returns:
            Path to the created batch file
        """
        batch_file_path = self.output_dir / "batch_requests.jsonl"

        with open(batch_file_path, 'w', encoding='utf-8') as f:
            for request in requests:
                f.write(json.dumps(request) + '\n')

        logger.info(f"Created batch file: {batch_file_path}")
        return str(batch_file_path)

    async def submit_batch_job(self, batch_file_path: str) -> str:
        """
        Submit a batch job to OpenAI API.

        Args:
            batch_file_path: Path to the batch file

        Returns:
            Batch job ID
        """
        # Upload the batch file
        with open(batch_file_path, 'rb') as f:
            batch_input_file = self.client.files.create(
                file=f,
                purpose="batch"
            )

        # Create the batch job
        batch_job = self.client.batches.create(
            input_file_id=batch_input_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h",
            metadata={"description": "KULLM Pro code switching batch"}
        )

        logger.info(f"Submitted batch job: {batch_job.id}")
        return batch_job.id

    async def wait_for_batch_completion(self, batch_id: str,
                                      check_interval: int = 60) -> Dict[str, Any]:
        """
        Wait for batch job completion.

        Args:
            batch_id: Batch job ID
            check_interval: Check interval in seconds

        Returns:
            Completed batch job information
        """
        logger.info(f"Waiting for batch {batch_id} to complete...")

        while True:
            batch_job = self.client.batches.retrieve(batch_id)

            if batch_job.status == "completed":
                logger.info(f"Batch {batch_id} completed successfully")
                return batch_job
            elif batch_job.status == "failed":
                raise Exception(f"Batch {batch_id} failed: {batch_job.errors}")
            elif batch_job.status == "cancelled":
                raise Exception(f"Batch {batch_id} was cancelled")

            logger.info(f"Batch status: {batch_job.status}. Checking again in {check_interval} seconds...")
            await asyncio.sleep(check_interval)

    def download_batch_results(self, batch_job: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Download and parse batch results.

        Args:
            batch_job: Completed batch job information

        Returns:
            List of batch results
        """
        # Download the output file
        output_file_id = batch_job.output_file_id
        output_content = self.client.files.content(output_file_id)

        # Parse results
        results = []
        for line in output_content.text.split('\n'):
            if line.strip():
                results.append(json.loads(line))

        logger.info(f"Downloaded {len(results)} batch results")
        return results

    def apply_translations(self, original_data: List[Dict[str, Any]],
                          batch_results: List[Dict[str, Any]],
                          text_columns: List[str]) -> List[Dict[str, Any]]:
        """
        Apply translations to original data.

        Args:
            original_data: Original dataset
            batch_results: Batch translation results
            text_columns: Columns that were translated

        Returns:
            Code-switched dataset
        """
        # Create a mapping of custom_id to translation
        translations = {}
        for result in batch_results:
            if result.get('response') and result['response'].get('body'):
                custom_id = result['custom_id']
                content = result['response']['body']['choices'][0]['message']['content']
                translations[custom_id] = content

        # Apply translations to create code-switched data
        code_switched_data = []
        for i, sample in enumerate(original_data):
            new_sample = sample.copy()
            for col in text_columns:
                custom_id = f"sample_{i}_{col}"
                if custom_id in translations:
                    new_sample[col] = translations[custom_id]
            code_switched_data.append(new_sample)

        logger.info(f"Applied translations to {len(code_switched_data)} samples")
        return code_switched_data

    async def process_dataset(self, dataset_name: str, split: str = "train",
                            subset: Optional[str] = None, n_samples: Optional[int] = None,
                            text_columns: Optional[List[str]] = None) -> Tuple[str, str]:
        """
        Process a dataset for code switching.

        Args:
            dataset_name: Name of the Hugging Face dataset
            split: Dataset split to process
            subset: Dataset subset/configuration
            n_samples: Number of samples to process (shortest by character count)
            text_columns: List of columns to translate (auto-detect if None)

        Returns:
            Tuple of (original_file_path, code_switched_file_path)
        """
        logger.info(f"Processing dataset: {dataset_name}")
        logger.info(f"Split: {split}, Subset: {subset}, Samples: {n_samples}")

        # Load dataset
        dataset = load_hf_dataset(dataset_name, split, subset)
        data = list(dataset)

        # Filter to shortest samples if n_samples specified
        if n_samples and n_samples < len(data):
            data = filter_shortest_samples(data, n_samples)

        # Auto-detect text columns if not provided
        if text_columns is None:
            text_columns = []
            if data:
                sample = data[0]
                for key, value in sample.items():
                    if isinstance(value, str) and len(value) > 10:  # Reasonable text length
                        text_columns.append(key)
            logger.info(f"Auto-detected text columns: {text_columns}")

        # Generate filenames
        base_filename = create_output_filename(dataset_name, split, subset, len(data))
        original_filename = f"original_{base_filename}"
        code_switched_filename = f"code_switched_{base_filename}"

        original_path = self.output_dir / original_filename
        code_switched_path = self.output_dir / code_switched_filename

        # Save original data
        save_jsonl(data, original_path)

        # Prepare and submit batch job
        requests = self.prepare_batch_requests(data, text_columns)
        batch_file_path = self.create_batch_file(requests)

        batch_id = await self.submit_batch_job(batch_file_path)
        batch_job = await self.wait_for_batch_completion(batch_id)

        # Download results and apply translations
        batch_results = self.download_batch_results(batch_job)
        code_switched_data = self.apply_translations(data, batch_results, text_columns)

        # Save code-switched data
        save_jsonl(code_switched_data, code_switched_path)

        # Cleanup batch file
        os.remove(batch_file_path)

        logger.info(f"Code switching completed!")
        logger.info(f"Original file: {original_path}")
        logger.info(f"Code-switched file: {code_switched_path}")

        return str(original_path), str(code_switched_path)


class CodeSwitchCLI:
    """
    Command-line interface for code switching using Python Fire.
    """

    def __init__(self):
        """Initialize the CLI."""
        self.logger = logger

    def run(self, dataset_name: str, split: str = "train", subset: Optional[str] = None,
            n_samples: Optional[int] = None, output_dir: str = "./data",
            text_columns: Optional[str] = None):
        """
        Run code switching on a dataset.

        Args:
            dataset_name: Name of the Hugging Face dataset (e.g., "GAIR/LIMO")
            split: Dataset split to process (default: "train")
            subset: Dataset subset/configuration (default: None)
            n_samples: Number of shortest samples to process (default: None for all)
            output_dir: Output directory for files (default: "./data")
            text_columns: Comma-separated list of columns to translate (auto-detect if None)

        Example:
            python src/code_switch.py run --dataset_name="GAIR/LIMO" --split="train" --n_samples=300
        """
        # Parse text_columns if provided
        if text_columns:
            text_columns = [col.strip() for col in text_columns.split(',')]
        else:
            text_columns = None

        # Create pipeline
        pipeline = CodeSwitchingPipeline(output_dir=output_dir)

        # Run the processing
        try:
            original_file, code_switched_file = asyncio.run(
                pipeline.process_dataset(
                    dataset_name=dataset_name,
                    split=split,
                    subset=subset,
                    n_samples=n_samples,
                    text_columns=text_columns
                )
            )

            self.logger.info("Code switching completed successfully!")
            self.logger.info(f"Original file: {original_file}")
            self.logger.info(f"Code-switched file: {code_switched_file}")

            return {
                "original_file": original_file,
                "code_switched_file": code_switched_file,
                "status": "success"
            }

        except Exception as e:
            self.logger.error(f"Code switching failed: {e}")
            raise


def main():
    """Main entry point for the CLI."""
    fire.Fire(CodeSwitchCLI)


if __name__ == "__main__":
    main()
