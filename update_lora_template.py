#!/usr/bin/env python3
"""
Update existing LoRA adapter with the new think-aware chat template.
"""

import sys
import argparse
from pathlib import Path

# Add src to path
sys.path.append('src')

from utils.model_utils import setup_tokenizer_with_think_tokens, save_tokenizer_with_think_template


def main():
    parser = argparse.ArgumentParser(description="Update LoRA adapter with new think-aware template")
    parser.add_argument("lora_path", help="Path to LoRA adapter directory")
    parser.add_argument("--base_model", default="Qwen/Qwen2.5-7B-Instruct", 
                       help="Base model name")
    
    args = parser.parse_args()
    
    lora_path = Path(args.lora_path)
    
    if not lora_path.exists():
        print(f"❌ LoRA path does not exist: {lora_path}")
        return 1
    
    print(f"🔄 Updating LoRA adapter: {lora_path}")
    print(f"📋 Base model: {args.base_model}")
    
    try:
        # Create tokenizer with updated think-aware template
        tokenizer = setup_tokenizer_with_think_tokens(args.base_model)
        
        # Save updated tokenizer
        save_tokenizer_with_think_template(tokenizer, str(lora_path))
        
        print("✅ Successfully updated LoRA adapter!")
        
        # Test the template
        messages = [{"role": "user", "content": "What is 15 + 27?"}]
        
        think_on = tokenizer.apply_chat_template(
            messages, add_generation_prompt=True, tokenize=False, think_mode=True
        )
        think_off = tokenizer.apply_chat_template(
            messages, add_generation_prompt=True, tokenize=False, think_mode=False
        )
        
        print("\n🧪 Template Test:")
        print("Think ON ends with:", repr(think_on.split('assistant\n')[-1]))
        print("Think OFF ends with:", repr(think_off.split('assistant\n')[-1]))
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
