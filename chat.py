import os
import time
import torch
import warnings
import re
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel

# Suppress warnings for cleaner inference output
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"


def load_model_and_tokenizer(model_path, lora_path=None):
    """
    Load model and tokenizer with optional LoRA adapter support.

    Args:
        model_path: Path to base model or fine-tuned model
        lora_path: Optional path to LoRA adapter

    Returns:
        tuple: (model, tokenizer)
    """
    # If LoRA path is provided, try to load tokenizer from LoRA path first
    # This ensures vocabulary compatibility
    if lora_path and os.path.exists(os.path.join(lora_path, "tokenizer.json")):
        print(f"Loading tokenizer from LoRA path: {lora_path}")
        tokenizer = AutoTokenizer.from_pretrained(lora_path, trust_remote_code=True)
    else:
        print(f"Loading tokenizer from: {model_path}")
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)

    # Set padding token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
        tokenizer.pad_token_id = tokenizer.eos_token_id

    print(f"Loading model from: {model_path}")
    model = AutoModelForCausalLM.from_pretrained(
        model_path, torch_dtype=torch.float16, device_map="auto", trust_remote_code=True
    )

    # Load LoRA adapter if provided
    if lora_path:
        print(f"Loading LoRA adapter from: {lora_path}")

        # Check vocabulary compatibility and handle mismatches
        model_vocab_size = model.get_input_embeddings().weight.shape[0]
        tokenizer_vocab_size = len(tokenizer)

        print(f"Model vocab size: {model_vocab_size}")
        print(f"Tokenizer vocab size: {tokenizer_vocab_size}")

        if model_vocab_size != tokenizer_vocab_size:
            print(f"⚠️ Vocabulary size mismatch detected!")

            # Check if LoRA adapter expects a specific vocabulary size
            adapter_config_path = os.path.join(lora_path, "adapter_config.json")
            if os.path.exists(adapter_config_path):
                import json

                with open(adapter_config_path, "r") as f:
                    adapter_config = json.load(f)
                print(
                    f"LoRA adapter base model: {adapter_config.get('base_model_name_or_path', 'Unknown')}"
                )

            # Resize model embeddings to match tokenizer
            print(
                f"Resizing model embeddings from {model_vocab_size} to {tokenizer_vocab_size}"
            )
            model.resize_token_embeddings(tokenizer_vocab_size)
            print("✅ Model embeddings resized successfully")

        try:
            model = PeftModel.from_pretrained(model, lora_path)
            print("✅ LoRA adapter loaded successfully")
        except Exception as e:
            print(f"❌ Failed to load LoRA adapter: {e}")
            print("💡 Continuing with base model only...")

    print("✅ Model loaded successfully")
    return model, tokenizer


def apply_think_chat_template(
    tokenizer, messages, think_mode=True, add_generation_prompt=True
):
    """
    Apply chat template with think token support.

    Args:
        tokenizer: The tokenizer
        messages: List of message dictionaries
        think_mode: Whether to enable think tokens (default: True)
        add_generation_prompt: Whether to add generation prompt

    Returns:
        str: Formatted chat text with think token support
    """
    # Apply the standard chat template
    text = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=add_generation_prompt
    )

    # If adding generation prompt, modify based on think mode
    if add_generation_prompt:
        if think_mode:
            # Add <think> to trigger thinking mode
            # Template ends with: <|im_start|>assistant\n<think>
            text += "<think>"
        else:
            # Add <think></think> to skip thinking mode
            # Template ends with: <|im_start|>assistant\n<think></think>
            text += "<think></think>"

    return text


def generate_response(model, tokenizer, text, max_new_tokens=512, **kwargs):
    """
    Generate response using model directly (supports both base models and LoRA).

    Args:
        model: The model (base or LoRA)
        tokenizer: Tokenizer
        text: Input text
        max_new_tokens: Maximum tokens to generate
        **kwargs: Additional generation parameters

    Returns:
        str: Generated response
    """
    # Tokenize input
    inputs = tokenizer(text, return_tensors="pt", padding=True, truncation=True)

    # Move to model device
    if hasattr(model, "device"):
        inputs = {k: v.to(model.device) for k, v in inputs.items()}
    elif torch.cuda.is_available():
        inputs = {k: v.to("cuda") for k, v in inputs.items()}

    # Generate
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_new_tokens,
            do_sample=kwargs.get("do_sample", False),
            temperature=kwargs.get("temperature", 1.0),
            top_p=kwargs.get("top_p", 1.0),
            top_k=kwargs.get("top_k", 50),
            repetition_penalty=kwargs.get("repetition_penalty", 1.0),
            pad_token_id=tokenizer.pad_token_id,
            eos_token_id=tokenizer.eos_token_id,
            use_cache=True,
        )

    # Decode only the new tokens
    input_length = inputs["input_ids"].shape[1]
    generated_tokens = outputs[0][input_length:]
    response = tokenizer.decode(
        generated_tokens, skip_special_tokens=False, clean_up_tokenization_spaces=True
    )

    # Clean up the response
    if response.endswith("<|im_end|>"):
        response = response[:-10]  # Remove <|im_end|>

    return response.strip()


def format_response_with_think_tokens(text):
    """
    Format response text to display think tokens in different colors.

    Args:
        text: Response text that may contain think tokens

    Returns:
        str: Formatted text with colored think tokens
    """
    # ANSI color codes
    THINK_COLOR = "\033[96m"  # Cyan for think tokens
    CONTENT_COLOR = "\033[94m"  # Blue for think content
    RESET_COLOR = "\033[0m"  # Reset to default
    ASSISTANT_COLOR = "\033[95m"  # Magenta for assistant text

    # Check if think tokens exist
    if "<think>" in text and "</think>" in text:
        # Replace think tokens with colored versions
        text = re.sub(
            r"<think>(.*?)</think>",
            f"{THINK_COLOR}<think>{CONTENT_COLOR}\\1{THINK_COLOR}</think>{RESET_COLOR}",
            text,
            flags=re.DOTALL,
        )
        return f"{ASSISTANT_COLOR}Assistant: {RESET_COLOR}{text}"
    else:
        # No think tokens, use regular assistant color
        return f"{ASSISTANT_COLOR}Assistant: {text}{RESET_COLOR}"


def get_default_system_prompt():
    """Get the default system prompt for KULLM-Pro with think tokens."""
    return """You are KULLM-Pro, a hybrid thinking model developed by Korea University NLP&AI Lab. You can show your reasoning process using think tokens when needed.

IMPORTANT: You MUST use <think> and </think> tags to show your reasoning process.

Format your responses EXACTLY like this:
<think>
[Your detailed step-by-step reasoning here]
</think>
[Your final answer here]

Example:
User: What is 15 + 27?
Assistant: <think>I need to add 15 and 27. Let me do this step by step: 15 + 27 = 42.</think>The answer is 42.

Always use this format - think tokens are required for all responses."""


def main(
    model_path: str,
    lora_path: str = None,
    sys_prompt: str = None,
    max_new_tokens: int = 512,
    think_mode: bool = True,
    **kwargs,
):
    """
    Interactive chat interface with support for LoRA adapters and think tokens.

    Args:
        model_path: Path to base model or fine-tuned model
        lora_path: Optional path to LoRA adapter
        sys_prompt: System prompt (auto-loaded from code_switch.txt if available)
        max_new_tokens: Maximum tokens to generate
        **kwargs: Additional generation parameters
    """
    print("🚀 KULLM-Pro Chat Interface")
    print("=" * 50)
    print(f"Model path: {model_path}")
    if lora_path:
        print(f"LoRA path: {lora_path}")
    print()

    # Load model and tokenizer
    model, tokenizer = load_model_and_tokenizer(model_path, lora_path)

    # Display tokenizer info
    print(f"BOS token: {tokenizer.bos_token} (ID: {tokenizer.bos_token_id})")
    print(f"EOS token: {tokenizer.eos_token} (ID: {tokenizer.eos_token_id})")

    # Check for think tokens
    think_tokens_available = False
    if hasattr(tokenizer, "additional_special_tokens"):
        special_tokens = tokenizer.additional_special_tokens or []
        if "<think>" in special_tokens and "</think>" in special_tokens:
            think_tokens_available = True
            print("✅ Think tokens detected: <think>, </think>")

    if not think_tokens_available:
        print("ℹ️ No think tokens detected")

    print()

    # Model is ready for generation
    # Initialize conversation with system prompt
    messages = []

    # Load system prompt (default or custom)
    if sys_prompt is None:
        sys_prompt = get_default_system_prompt()
        print("✅ Using default reasoning system prompt")
    else:
        print("✅ Using custom system prompt")

    messages.append({"role": "system", "content": sys_prompt})
    print(f"📝 System prompt loaded ({len(sys_prompt)} characters)")

    print("\n💬 Chat started! Commands: 'clear' to reset, 'exit' to quit")
    print("=" * 50)

    while True:
        try:
            # Get user input
            input_ = input("\033[94m👤 Enter instruction: \033[0m")

            # Handle special commands
            if input_.strip().lower() == "clear":
                messages = []
                messages.append({"role": "system", "content": sys_prompt})
                os.system("clear")
                print("🚀 KULLM-Pro Chat Interface")
                print("=" * 50)
                print("🔄 Conversation cleared!")
                print("=" * 50)
                continue
            elif input_.strip().lower() == "exit":
                print("\n👋 Goodbye!")
                break
            elif input_.strip() == "":
                continue

            # Add user message
            messages.append({"role": "user", "content": input_})

            # Clear screen and show conversation history
            os.system("clear")
            print("🚀 KULLM-Pro Chat Interface")
            print("=" * 50)

            # Display conversation history (excluding system prompt)
            for m in messages:
                if m["role"] == "system":
                    continue
                elif m["role"] == "user":
                    print(f"\033[93m👤 User: {m['content']}\033[0m")
                elif m["role"] == "assistant":
                    print(format_response_with_think_tokens(m["content"]))

            # Show current user input
            print(f"\033[93m👤 User: {input_}\033[0m")

            # Generate response
            print("\033[90m🤔 Thinking...\033[0m")
            start = time.time()

            # Apply chat template with think token support
            text = tokenizer.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=False,
                think_mode=think_mode,
            )

            # Generate response
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                result = generate_response(
                    model, tokenizer, text, max_new_tokens=max_new_tokens, **kwargs
                )

            # Add assistant response to conversation
            messages.append({"role": "assistant", "content": result})

            # Display formatted response
            print(format_response_with_think_tokens(result))
            print(f"\033[90m⏱️ Response time: {time.time() - start:.2f}s\033[0m")
            print("=" * 50)

        except KeyboardInterrupt:
            print("\n\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type 'exit' to quit.")
            continue


if __name__ == "__main__":
    import fire

    fire.Fire(main)
