# KULLM Pro: Korean Reasoning Language Model with Think Tokens

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://www.python.org/downloads/)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.0%2B-red.svg)](https://pytorch.org/)

KULLM Pro is a production-ready framework for fine-tuning reasoning models using structured thinking tokens. The model learns to reason step-by-step using `<think>` and `</think>` tokens, enabling transparent and improved mathematical problem-solving capabilities.

## 🌟 Key Features

- **Think Token Reasoning**: Transparent step-by-step reasoning with `<think>` and `</think>` tokens
- **LoRA Fine-tuning**: Efficient parameter-efficient fine-tuning with Low-Rank Adaptation
- **Selective Code Switching**: Automated English-to-Korean translation of solution content only
- **Production Ready**: Clean, modular codebase with comprehensive error handling
- **Experiment Tracking**: Integrated Weights & Biases support for monitoring training
- **Flexible Dataset Processing**: Support for any Hugging Face dataset with automatic formatting
- **Advanced Training Features**: Accelerate library, gradient checkpointing, mixed precision
- **Checkpoint Management**: Automatic saving, loading, and resumable training

## 🚀 Quick Start

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/junkim100/KULLM-Pro.git
   cd KULLM-Pro
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys:
   # OPENAI_API_KEY=your_openai_api_key_here
   # WANDB_API_KEY=your_wandb_api_key_here
   # HF_TOKEN=your_huggingface_token_here (optional)
   ```

### Basic Usage

#### Code Switching (English to Korean)

Process any Hugging Face dataset and generate Korean translations for the solution column only:

```bash
# Process LIMO dataset with 300 shortest samples
python src/code_switch.py run --dataset_name="GAIR/LIMO" --split="train" --n_samples=300

# Process with custom parameters
python src/code_switch.py run \
    --dataset_name="GAIR/LIMO" \
    --split="train" \
    --n_samples=100 \
    --output_dir="./data"

# Use custom system prompt file
python src/code_switch.py run \
    --dataset_name="GAIR/LIMO" \
    --split="train" \
    --n_samples=300 \
    --system_prompt_path="custom_prompt.txt"
```

**Important**: The code switching functionality specifically:
- Only translates the `solution` column content to Korean
- Preserves the original `question` and `answer` columns unchanged
- Uses the system prompt from `system_prompt.txt` for translation instructions
- Outputs JSONL files with the same three-column structure: `question`, `solution`, `answer`

#### Fine-tuning with Think Tokens

Train a reasoning model with LoRA fine-tuning:

```bash
# Basic fine-tuning
python src/fine_tune.py train \
    --data_file="./data/train.jsonl" \
    --output_dir="./outputs/my_model"

# Advanced fine-tuning with validation
python src/fine_tune.py train \
    --data_file="./data/train.jsonl" \
    --val_file="./data/val.jsonl" \
    --output_dir="./outputs/kullm_pro_reasoning" \
    --model_name="Qwen/Qwen2.5-7B-Instruct" \
    --run_name="experiment_1"

# Fine-tuning with custom config
python src/fine_tune.py train \
    --data_file="./data/train.jsonl" \
    --output_dir="./outputs/my_model" \
    --config_file="custom_config.yaml"
```

## 📁 Project Structure

```
KULLM-Pro/
├── src/                          # Main source code
│   ├── __init__.py              # Package initialization
│   ├── code_switch.py           # Code switching CLI
│   ├── fine_tune.py             # Fine-tuning CLI
│   └── utils/                   # Utility modules
│       ├── __init__.py
│       ├── data_processing.py   # Data processing utilities
│       └── model_utils.py       # Model utilities
├── config.yaml                  # Configuration file
├── requirements.txt             # Dependencies
├── .env.example                 # Environment variables template
├── system_prompt.txt            # System prompt for code switching
├── data/                        # Data directory
├── LICENSE                      # License file
└── .gitignore                   # Git ignore file
```

## ⚙️ Configuration

All training and processing parameters are configured through `config.yaml`. Key sections include:

### Model Configuration
```yaml
model:
  name: "Qwen/Qwen2.5-7B-Instruct"  # Base model to fine-tune
  max_length: 2048                   # Maximum sequence length
  torch_dtype: "float16"             # Model precision
```

### Training Configuration
```yaml
training:
  num_train_epochs: 3                # Number of training epochs
  per_device_train_batch_size: 2     # Training batch size per device
  learning_rate: 2e-4                # Learning rate
  gradient_accumulation_steps: 8     # Gradient accumulation steps
  fp16: true                         # Use mixed precision training
```

### LoRA Configuration
```yaml
lora:
  r: 16                            # LoRA rank
  alpha: 32                        # LoRA alpha parameter
  dropout: 0.1                     # LoRA dropout
  target_modules: ["q_proj", "k_proj", "v_proj", "o_proj"]
```

### Code Switching Configuration
```yaml
code_switching:
  model: "gpt-4o-mini"               # OpenAI model for translation
  temperature: 0.1                   # Temperature for translation
  max_tokens: 4000                   # Maximum tokens per request
  batch_size: 100                    # Number of requests per batch
```

## 🔧 API Reference

### Code Switching Pipeline

```python
from src.code_switch import CodeSwitchingPipeline

# Initialize pipeline with custom system prompt
pipeline = CodeSwitchingPipeline(
    output_dir="./data",
    system_prompt_path="system_prompt.txt"
)

# Process dataset (only translates solution column)
original_file, code_switched_file = await pipeline.process_dataset(
    dataset_name="GAIR/LIMO",
    split="train",
    n_samples=300
)
```

### Fine-tuning Pipeline

```python
from src.fine_tune import FineTuningPipeline

# Initialize pipeline
pipeline = FineTuningPipeline(config_path="config.yaml")

# Train model
result = pipeline.train(
    data_file="./data/train.jsonl",
    output_dir="./outputs/my_model",
    model_name="Qwen/Qwen2.5-7B-Instruct",
    val_file="./data/val.jsonl"  # Optional validation file
)
```

### Utility Functions

```python
from src.utils.data_processing import load_jsonl, save_jsonl, filter_shortest_samples
from src.utils.model_utils import load_model_and_tokenizer, print_model_info

# Load and process data
data = load_jsonl("./data/train.jsonl")
filtered_data = filter_shortest_samples(data, n_samples=500)

# Load model with think tokens
model, tokenizer = load_model_and_tokenizer(
    "Qwen/Qwen2.5-7B-Instruct",
    think_start="<think>",
    think_end="</think>"
)

# Print model information
print_model_info(model, tokenizer)
```

## 📊 Data Format

### Input Data Format

The system expects JSONL files with three columns:
```json
{
  "question": "What is 2 + 2?",
  "solution": "To solve this problem, I need to add the two numbers together. 2 + 2 = 4.",
  "answer": "4"
}
```

### Code Switching Output

The code switching pipeline generates two files:
- `original_{dataset}_{split}_{n_samples}.jsonl`: Original data with all three columns
- `code_switched_{dataset}_{split}_{n_samples}.jsonl`: Same structure with only the `solution` column translated to Korean

### Training Data Format

For fine-tuning, the system automatically formats data as:
```json
{
  "text": "<think>solution_content</think>answer_content",
  "input": "question_content",
  "output": "<think>solution_content</think>answer_content"
}
```

## 🔍 Environment Variables

Required environment variables (see `.env.example`):

- `OPENAI_API_KEY`: OpenAI API key for code switching
- `WANDB_API_KEY`: Weights & Biases API key for experiment tracking
- `HF_TOKEN`: Hugging Face token (optional, for private models)

## 📈 Monitoring and Logging

KULLM Pro integrates with Weights & Biases for comprehensive experiment tracking:

- Training metrics and loss curves
- Model parameters and hyperparameters
- Dataset information and preprocessing steps
- Hardware utilization and performance metrics

## 🚨 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or enable gradient checkpointing
2. **OpenAI API Limits**: Adjust batch size in code switching configuration
3. **Wandb Login Issues**: Ensure `WANDB_API_KEY` is set correctly

### Performance Optimization

- Use mixed precision training (`fp16: true`)
- Enable gradient checkpointing for large models
- Adjust batch size and gradient accumulation steps
- Use appropriate LoRA rank (16-64 typically works well)

## 📝 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📧 Contact

For questions and support, please open an issue on GitHub.

---

**KULLM Pro** - Advancing Korean language AI through transparent reasoning and efficient fine-tuning.
